"""
Specialized AI agents for the VPS Admin Orchestrator system.
Contains individual agent classes for different tasks.
"""

import asyncio
import json
import re
import traceback
from typing import List, Dict, Any, Optional, Union, Tuple
from abc import ABC, abstractmethod

import google.generativeai as genai

from config import Config
from models import (
    AgentResponse, PlannerResponse, ExecutorResponse,
    RefinerResponse, SummarizerResponse, ErrorRecoveryResponse, SSHResult
)


def clean_and_parse_json(response_text: str) -> Tuple[Union[dict, list], str]:
    """
    Robust JSON parsing that handles markdown code blocks and escape characters.

    Returns:
        tuple: (parsed_data, cleaned_response)

    Raises:
        json.JSONDecodeError: If JSON parsing fails after all cleaning attempts
    """
    if not response_text or not response_text.strip():
        raise json.JSONDecodeError("Empty response", "", 0)

    # Step 1: Remove markdown code blocks
    cleaned_response = response_text.strip()

    # Handle various markdown patterns
    markdown_patterns = [
        (r'^```json\s*\n?', ''),  # ```json at start
        (r'^```\s*\n?', ''),     # ``` at start
        (r'\n?```\s*$', ''),     # ``` at end
        (r'^`{1,2}', ''),        # Single or double backticks at start
        (r'`{1,2}$', ''),        # Single or double backticks at end
    ]

    for pattern, replacement in markdown_patterns:
        cleaned_response = re.sub(pattern, replacement, cleaned_response, flags=re.MULTILINE)

    cleaned_response = cleaned_response.strip()

    # Step 2: Handle common escape character issues
    # Fix common problematic escape sequences that might appear in AI responses
    escape_fixes = [
        (r'\\(?!["\\/bfnrt])', r'\\\\'),  # Fix single backslashes that aren't valid escapes
        (r'(?<!\\)\\(?=\s)', r'\\\\'),    # Fix trailing backslashes before whitespace
    ]

    for pattern, replacement in escape_fixes:
        cleaned_response = re.sub(pattern, replacement, cleaned_response)

    # Step 3: Try to parse JSON
    try:
        parsed_data = json.loads(cleaned_response)
        return parsed_data, cleaned_response
    except json.JSONDecodeError as e:
        # Step 4: Additional cleaning for common issues
        # Remove any remaining non-JSON content before/after the JSON
        json_match = re.search(r'(\[.*\]|\{.*\})', cleaned_response, re.DOTALL)
        if json_match:
            json_only = json_match.group(1)
            try:
                parsed_data = json.loads(json_only)
                return parsed_data, json_only
            except json.JSONDecodeError:
                pass

        # If all else fails, re-raise the original error
        raise e


class BaseAgent(ABC):
    """Base class for all specialized agents."""

    def __init__(self, config: Config, model_name: Optional[str] = None):
        self.config = config
        self.model_name = model_name or config.GEMINI_MODEL_NAME
        self.model = None
        self.ai_client = None
        self._initialize_model()
        self._initialize_ai_client()

    def _initialize_model(self):
        """Initialize the AI model for this agent."""
        try:
            genai.configure(api_key=self.config.GEMINI_API_KEY)
            self.model = genai.GenerativeModel(self.model_name)
            print(f"Initialized {self.__class__.__name__} with model: {self.model_name}")
        except Exception as e:
            print(f"FATAL: Error initializing {self.__class__.__name__}: {e}")
            raise

    def _initialize_ai_client(self):
        """Initialize the AI client for thinking budget functionality."""
        try:
            # Import here to avoid circular imports
            from ai_client import AIClient
            self.ai_client = AIClient(self.config)
            print(f"Initialized AI client for {self.__class__.__name__}")
        except Exception as e:
            print(f"WARNING: Could not initialize AI client for {self.__class__.__name__}: {e}")
            self.ai_client = None

    @abstractmethod
    async def process(self, **kwargs) -> AgentResponse:
        """Process the agent's specific task."""
        pass

    async def _generate_response(self, prompt: str, timeout: int = 60) -> str:
        """Generate response from the AI model."""
        try:
            response = await asyncio.wait_for(
                asyncio.to_thread(self.model.generate_content, prompt),
                timeout=timeout
            )

            if not response or not hasattr(response, 'text'):
                raise ValueError("AI response was empty or blocked")

            return response.text.strip()
        except asyncio.TimeoutError:
            raise TimeoutError(f"AI call timed out after {timeout} seconds")
        except Exception as e:
            print(f"ERROR in {self.__class__.__name__}: {e}")
            raise


class PlannerAgent(BaseAgent):
    """Agent responsible for breaking down user requests into executable steps."""

    async def process(self, user_prompt: str, system_info: str, percentage: int = 70, **kwargs) -> PlannerResponse:
        """Break down user request into a structured plan with commands."""

        prompt = f"""You are an expert Linux System Administrator AI. Your task is to break down a user's request into a series of clear, sequential, and executable steps WITH their corresponding commands.

IMPORTANT:
- Be intelligent and efficient - avoid unnecessary steps
- Include specific details from the user request (URLs, names, paths, etc.)
- Generate BOTH the description AND the actual command for each step
- Keep the plan concise but complete

System Information:
{system_info}

User Request: "{user_prompt}"

Instructions:
1. Analyze the user's request carefully and extract ALL specific details (URLs, repository names, paths, etc.)
2. Break it down into logical, sequential steps (aim for 3-5 steps for most tasks)
3. Each step should include both a clear description AND the actual shell command
4. Use the EXACT URLs, names, and details from the user's request - do NOT use placeholders
5. Consider dependencies between steps
6. Make commands non-interactive (use -y flags, etc.)
7. Use sudo when root privileges are required

Respond ONLY with a JSON array of objects, where each object has "description" and "command" keys.

Example format:
[
  {{"description": "Update package lists", "command": "sudo apt update"}},
  {{"description": "Install nginx web server", "command": "sudo apt install -y nginx"}},
  {{"description": "Clone repository from https://github.com/user/repo", "command": "sudo git clone https://github.com/user/repo /var/www/html/app"}},
  {{"description": "Start and enable nginx service", "command": "sudo systemctl start nginx && sudo systemctl enable nginx"}}
]

Your JSON response:"""

        try:
            # Use thinking budget for complex tasks (percentage > 50%)
            if (percentage > self.config.MIN_THINKING_PERCENTAGE and
                self.ai_client and
                hasattr(self.ai_client, 'generate_response_with_thinking')):
                print(f"[PlannerAgent] Using thinking budget for {percentage}% difficulty task")
                response_text = await asyncio.wait_for(
                    self.ai_client.generate_response_with_thinking(
                        task_id="planner",
                        prompt=prompt,
                        percentage=percentage
                    ),
                    timeout=150  # 2.5 minutes timeout for thinking generation
                )
            else:
                print(f"[PlannerAgent] Using regular generation for {percentage}% difficulty task")
                response_text = await asyncio.wait_for(
                    self._generate_response(prompt),
                    timeout=90  # 1.5 minutes timeout for regular generation
                )

            # Check if response is empty
            if not response_text or not response_text.strip():
                raise ValueError("AI response was empty")

            # Parse JSON response using robust parsing function
            try:
                # Additional debug logging
                print(f"[PlannerAgent] Raw response length: {len(response_text)}")
                print(f"[PlannerAgent] First 100 chars of raw response: {response_text[:100]}")

                plan_data, cleaned_response = clean_and_parse_json(response_text)

                print(f"[PlannerAgent] Cleaned response length: {len(cleaned_response)}")
                print(f"[PlannerAgent] First 100 chars of cleaned response: {cleaned_response[:100]}")

                if not isinstance(plan_data, list):
                    raise ValueError("Response is not a JSON array")

                # Validate structure
                for i, step in enumerate(plan_data):
                    if not isinstance(step, dict) or 'description' not in step:
                        raise ValueError(f"Step {i} missing 'description' field")
                    if 'command' not in step:
                        raise ValueError(f"Step {i} missing 'command' field")

                print(f"[PlannerAgent] Successfully parsed {len(plan_data)} steps")
                return PlannerResponse(
                    content=response_text,
                    plan_steps=plan_data,
                    estimated_steps=len(plan_data),
                    timestamp=asyncio.get_event_loop().time(),
                    metadata={
                        "user_prompt": user_prompt,
                        "total_steps": len(plan_data),
                        "task_type": "general"
                    }
                )

            except (json.JSONDecodeError, ValueError) as e:
                print(f"ERROR: Planner returned invalid JSON: {e}")
                print(f"Raw response: {response_text}")

                # Try to get cleaned response for debugging
                try:
                    _, cleaned_response = clean_and_parse_json(response_text)
                    print(f"Cleaned response: {cleaned_response}")
                except:
                    print("Could not clean response for debugging")

                # Fallback: create a simple single-step plan
                fallback_plan = [{"description": f"Execute user request: {user_prompt}", "command": "echo 'Please provide specific commands for this task'"}]
                return PlannerResponse(
                    content=json.dumps(fallback_plan),
                    plan_steps=fallback_plan,
                    estimated_steps=1,
                    timestamp=asyncio.get_event_loop().time(),
                    success=False,
                    metadata={
                        "error": f"JSON parsing failed: {str(e)}",
                        "raw_response": response_text
                    }
                )

        except asyncio.TimeoutError:
            print(f"ERROR: PlannerAgent timed out")
            # Create a fallback plan for timeout
            fallback_plan = [{"description": f"Execute user request: {user_prompt}", "command": "echo 'Planning timed out - please provide specific commands'"}]
            return PlannerResponse(
                content=json.dumps(fallback_plan),
                plan_steps=fallback_plan,
                estimated_steps=1,
                timestamp=asyncio.get_event_loop().time(),
                success=False,
                metadata={
                    "error": "PlannerAgent timed out",
                    "exception_type": "TimeoutError"
                }
            )

        except Exception as e:
            print(f"ERROR in PlannerAgent: {e}")
            print(f"[PlannerAgent] Exception type: {type(e).__name__}")

            # Create a fallback plan even for general exceptions
            fallback_plan = [{"description": f"Execute user request: {user_prompt}", "command": "echo 'Please provide specific commands for this task'"}]
            return PlannerResponse(
                content=json.dumps(fallback_plan),
                plan_steps=fallback_plan,
                estimated_steps=1,
                timestamp=asyncio.get_event_loop().time(),
                success=False,
                metadata={
                    "error": f"PlannerAgent exception: {str(e)}",
                    "exception_type": type(e).__name__
                }
            )


class ExecutorAgent(BaseAgent):
    """Agent responsible for converting plan steps into specific shell commands."""

    async def process(self, step_description: str, context_history: List[str],
                     system_info: str, **kwargs) -> ExecutorResponse:
        """Convert a plan step into a specific shell command."""

        context_str = "\n".join([f"- {ctx}" for ctx in context_history]) if context_history else "None"

        prompt = f"""You are an expert Linux System Administrator AI.
Based on the context and the current objective, provide the single, best, non-interactive shell command to accomplish the objective.

System Information:
{system_info}

Context of what has been done so far:
{context_str}

Current Objective: "{step_description}"

Instructions:
1. Generate ONE specific, non-interactive shell command
2. Use appropriate package managers (apt, yum, etc.) based on the system
3. Include necessary flags for non-interactive execution (-y, --assume-yes, etc.)
4. Use sudo when root privileges are required
5. Avoid interactive commands (nano, vim, crontab -e, etc.)
6. Respond with ONLY the shell command, nothing else

Your command:"""

        try:
            command = await self._generate_response(prompt)

            # Basic security analysis
            dangerous_patterns = [
                'rm -rf /', 'chmod 777', 'chown -R root', 'iptables -F',
                'ufw disable', 'systemctl stop ssh', 'passwd -d', 'sudo su -'
            ]

            security_risk = any(pattern in command.lower() for pattern in dangerous_patterns)

            # Determine command type
            command_type = "unknown"
            if any(pkg_cmd in command.lower() for pkg_cmd in ['apt', 'yum', 'dnf', 'install']):
                command_type = "package_management"
            elif any(svc_cmd in command.lower() for svc_cmd in ['systemctl', 'service']):
                command_type = "service_management"
            elif any(file_cmd in command.lower() for file_cmd in ['cp', 'mv', 'rm', 'chmod', 'chown']):
                command_type = "file_management"
            elif any(net_cmd in command.lower() for net_cmd in ['curl', 'wget', 'ssh', 'scp']):
                command_type = "network"

            return ExecutorResponse(
                content=command,
                command=command,
                command_type=command_type,
                security_risk=security_risk,
                timestamp=asyncio.get_event_loop().time(),
                metadata={
                    "step_description": step_description,
                    "context_items": len(context_history)
                }
            )

        except Exception as e:
            print(f"ERROR in ExecutorAgent: {e}")
            return ExecutorResponse(
                content=f"Error: {str(e)}",
                command="",
                timestamp=asyncio.get_event_loop().time(),
                success=False,
                metadata={"error": str(e)}
            )


class RefinerAgent(BaseAgent):
    """Agent responsible for analyzing failures and suggesting corrections."""

    async def process(self, step_description: str, failed_attempts: List[Dict[str, Any]],
                     **kwargs) -> RefinerResponse:
        """Analyze failed attempts and suggest a corrected command."""

        attempts_str = ""
        for i, attempt in enumerate(failed_attempts, 1):
            command = attempt.get('command', 'Unknown')
            error = attempt.get('error_message', 'Unknown error')
            stdout = attempt.get('stdout', '')
            stderr = attempt.get('stderr', '')
            exit_status = attempt.get('exit_status', 'Unknown')

            attempts_str += f"Attempt {i}:\n"
            attempts_str += f"- Command: {command}\n"
            attempts_str += f"- Exit Status: {exit_status}\n"
            attempts_str += f"- Error Message: {error}\n"

            if stdout and stdout.strip():
                stdout_preview = stdout[:300] + "..." if len(stdout) > 300 else stdout
                attempts_str += f"- Standard Output:\n{stdout_preview}\n"

            if stderr and stderr.strip():
                stderr_preview = stderr[:300] + "..." if len(stderr) > 300 else stderr
                attempts_str += f"- Standard Error:\n{stderr_preview}\n"

            attempts_str += "\n"

        prompt = f"""You are an expert Linux troubleshooter AI. Your goal is to fix a failed command by trying an alternative approach.

Original Objective: "{step_description}"

Failed Attempts:
{attempts_str}

Instructions:
1. Analyze what went wrong in the previous attempts by examining the stdout, stderr, and exit status
2. Identify the root cause of the failure
3. Can you try an alternative approach to achieve the same objective?
4. Provide a corrected command that addresses the specific error OR uses a completely different method
5. Ensure the command is non-interactive and safe
6. Consider package managers, different tools, or alternative methods if the original approach is fundamentally flawed
7. If permissions are the issue, suggest using sudo appropriately
8. If a package/service doesn't exist, suggest alternatives or proper installation methods

Respond with ONLY the corrected shell command, nothing else.

Your alternative approach command:"""

        try:
            corrected_command = await self._generate_response(prompt)

            # Extract original command from the most recent attempt
            original_command = failed_attempts[-1].get('command', '') if failed_attempts else ''

            # Generate analysis
            analysis = f"Analyzed {len(failed_attempts)} failed attempts with stdout/stderr details and suggested alternative approach to achieve the objective"

            return RefinerResponse(
                content=corrected_command,
                original_command=original_command,
                corrected_command=corrected_command,
                analysis=analysis,
                timestamp=asyncio.get_event_loop().time(),
                metadata={
                    "step_description": step_description,
                    "attempts_analyzed": len(failed_attempts)
                }
            )

        except Exception as e:
            print(f"ERROR in RefinerAgent: {e}")
            return RefinerResponse(
                content=f"Error: {str(e)}",
                original_command="",
                corrected_command="",
                analysis=f"Failed to analyze: {str(e)}",
                timestamp=asyncio.get_event_loop().time(),
                success=False,
                metadata={"error": str(e)}
            )


class SummarizerAgent(BaseAgent):
    """Agent responsible for creating concise summaries of completed steps."""

    async def process(self, step_description: str, command: str, ssh_result: SSHResult,
                     **kwargs) -> SummarizerResponse:
        """Create a concise summary of a completed step."""

        stdout_preview = ssh_result.stdout[:200] + "..." if len(ssh_result.stdout) > 200 else ssh_result.stdout
        stderr_preview = ssh_result.stderr[:200] + "..." if len(ssh_result.stderr) > 200 else ssh_result.stderr

        prompt = f"""You are an expert system administrator. Create a concise summary of what was accomplished in this step.

Step Description: "{step_description}"
Command Executed: {command}
Success: {ssh_result.success}
Exit Code: {ssh_result.exit_status}

Output (stdout):
{stdout_preview}

Errors (stderr):
{stderr_preview}

Instructions:
1. Create a brief, clear summary of what was accomplished
2. Focus on the key outcome, not the technical details
3. Mention any important results or changes made
4. Keep it to 1-2 sentences maximum
5. Use past tense (e.g., "Successfully installed...", "Configured...")

Your summary:"""

        try:
            summary = await self._generate_response(prompt)

            # Extract key outcomes from the result
            key_outcomes = []
            if ssh_result.success:
                if "installed" in ssh_result.stdout.lower():
                    key_outcomes.append("package_installed")
                if "started" in ssh_result.stdout.lower() or "active" in ssh_result.stdout.lower():
                    key_outcomes.append("service_started")
                if "enabled" in ssh_result.stdout.lower():
                    key_outcomes.append("service_enabled")
                if "created" in ssh_result.stdout.lower():
                    key_outcomes.append("file_created")

            return SummarizerResponse(
                content=summary,
                step_summary=summary,
                key_outcomes=key_outcomes,
                timestamp=asyncio.get_event_loop().time(),
                metadata={
                    "step_description": step_description,
                    "command": command,
                    "success": ssh_result.success,
                    "exit_code": ssh_result.exit_status
                }
            )

        except Exception as e:
            print(f"ERROR in SummarizerAgent: {e}")
            fallback_summary = f"Step completed: {step_description}"
            return SummarizerResponse(
                content=fallback_summary,
                step_summary=fallback_summary,
                key_outcomes=[],
                timestamp=asyncio.get_event_loop().time(),
                success=False,
                metadata={"error": str(e)}
            )


class ErrorRecoveryPlanner(BaseAgent):
    """Specialized agent for creating recovery plans when commands fail."""

    async def process(self, failed_command: str, error_details: Dict[str, Any],
                     original_objective: str, **kwargs) -> ErrorRecoveryResponse:
        """Create a recovery plan to fix a failed command and achieve the original objective."""

        stdout = error_details.get('stdout', '')
        stderr = error_details.get('stderr', '')
        exit_status = error_details.get('exit_status', 'Unknown')

        # Truncate long outputs for the prompt
        stdout_preview = stdout[:500] + "..." if len(stdout) > 500 else stdout
        stderr_preview = stderr[:500] + "..." if len(stderr) > 500 else stderr

        prompt = f"""You are an expert Linux troubleshooter and recovery specialist AI. A command has failed and you need to create a step-by-step recovery plan to fix the issue and achieve the original objective.

FAILED COMMAND DETAILS:
- Original Objective: "{original_objective}"
- Failed Command: {failed_command}
- Exit Status: {exit_status}
- Standard Output: {stdout_preview}
- Standard Error: {stderr_preview}

ANALYSIS REQUIRED:
1. Identify the root cause of the failure from the error messages
2. Determine what needs to be fixed or what alternative approach should be used
3. Create a step-by-step recovery plan to resolve the issue
4. Ensure the recovery plan will achieve the original objective

RECOVERY PLAN REQUIREMENTS:
- Break down the fix into 2-4 clear, sequential steps
- Each step should have both a description and a specific command
- Address the root cause, not just symptoms
- Use alternative approaches if the original method is fundamentally flawed
- Include any prerequisite fixes (permissions, missing packages, etc.)
- Make commands non-interactive and safe

COMMON FAILURE PATTERNS TO CONSIDER:
- Directory already exists: Use different approach or clean up first
- Permission denied: Add sudo or fix ownership
- Package not found: Update repositories or use correct package name
- Service conflicts: Stop conflicting services first
- Network issues: Check connectivity or use alternative sources
- Dependency issues: Install missing dependencies first
- Systemd not available: Use traditional 'service' command instead of 'systemctl'
- Init system issues: Detect system type and use appropriate service management
- Service management errors: Try alternative service control methods
- Apache ServerName warnings: Add ServerName directive to Apache configuration
- Apache configuration errors: Fix syntax errors, check virtual host configuration
- Port conflicts: Stop conflicting services or change port configuration
- SSL/TLS certificate issues: Generate or install proper certificates

Respond ONLY with a JSON array of recovery steps, where each object has "description" and "command" keys.

Example format:
[
  {{"description": "Remove existing conflicting directory", "command": "sudo rm -rf /var/www/html/portfolio"}},
  {{"description": "Clone repository to correct location", "command": "sudo git clone https://github.com/user/portfolio /var/www/html/portfolio"}},
  {{"description": "Set proper ownership for web files", "command": "sudo chown -R www-data:www-data /var/www/html/portfolio"}}
]

Your recovery plan JSON:"""

        try:
            response_text = await self._generate_response(prompt)

            # Parse JSON response using robust parsing function
            try:
                print(f"[ErrorRecoveryPlanner] Raw response length: {len(response_text)}")
                print(f"[ErrorRecoveryPlanner] First 100 chars of raw response: {response_text[:100]}")

                recovery_plan, cleaned_response = clean_and_parse_json(response_text)

                print(f"[ErrorRecoveryPlanner] Cleaned response length: {len(cleaned_response)}")
                print(f"[ErrorRecoveryPlanner] First 100 chars of cleaned response: {cleaned_response[:100]}")

                if not isinstance(recovery_plan, list):
                    raise ValueError("Response is not a JSON array")

                # Validate structure
                for i, step in enumerate(recovery_plan):
                    if not isinstance(step, dict) or 'description' not in step or 'command' not in step:
                        raise ValueError(f"Recovery step {i} missing required fields")

                # Determine recovery approach based on error analysis
                recovery_approach = "unknown"
                error_text = (stderr + " " + stdout).lower()
                if "already exists" in error_text:
                    recovery_approach = "cleanup_and_retry"
                elif "permission denied" in error_text:
                    recovery_approach = "fix_permissions"
                elif ("not found" in error_text or "no such" in error_text or
                      "unable to locate package" in error_text or "package not available" in error_text):
                    recovery_approach = "install_dependencies"
                elif "connection" in error_text or "network" in error_text:
                    recovery_approach = "network_alternative"
                elif ("systemd" in error_text or "systemctl" in error_text or
                      "init system" in error_text or "can't operate" in error_text or
                      "failed to connect to bus" in error_text):
                    recovery_approach = "service_management_alternative"
                elif ("apache" in error_text and ("could not reliably determine" in error_text or
                      "ah00558" in error_text or "servername" in error_text)):
                    recovery_approach = "apache_configuration_fix"
                elif ("apache" in error_text and ("syntax" in error_text or "config" in error_text)):
                    recovery_approach = "apache_syntax_fix"
                elif "port already in use" in error_text or "address already in use" in error_text:
                    recovery_approach = "port_conflict_resolution"
                else:
                    recovery_approach = "alternative_method"

                print(f"[ErrorRecoveryPlanner] Created recovery plan with {len(recovery_plan)} steps")
                return ErrorRecoveryResponse(
                    content=response_text,
                    recovery_plan=recovery_plan,
                    recovery_approach=recovery_approach,
                    estimated_recovery_steps=len(recovery_plan),
                    timestamp=asyncio.get_event_loop().time(),
                    metadata={
                        "failed_command": failed_command,
                        "original_objective": original_objective,
                        "error_type": recovery_approach,
                        "total_recovery_steps": len(recovery_plan)
                    }
                )

            except (json.JSONDecodeError, ValueError) as e:
                print(f"ERROR: ErrorRecoveryPlanner returned invalid JSON: {e}")
                print(f"Raw response: {response_text}")

                # Try to get cleaned response for debugging
                try:
                    _, cleaned_response = clean_and_parse_json(response_text)
                    print(f"Cleaned response: {cleaned_response}")
                except:
                    print("Could not clean response for debugging")

                # Fallback: create a simple retry plan
                fallback_plan = [
                    {"description": f"Retry the failed operation: {original_objective}",
                     "command": failed_command}
                ]
                return ErrorRecoveryResponse(
                    content=json.dumps(fallback_plan),
                    recovery_plan=fallback_plan,
                    recovery_approach="simple_retry",
                    estimated_recovery_steps=1,
                    timestamp=asyncio.get_event_loop().time(),
                    success=False,
                    metadata={
                        "error": f"JSON parsing failed: {str(e)}",
                        "raw_response": response_text
                    }
                )

        except Exception as e:
            print(f"ERROR in ErrorRecoveryPlanner: {e}")

            # Create a fallback recovery plan
            fallback_plan = [
                {"description": f"Manual intervention required for: {original_objective}",
                 "command": "echo 'Recovery planning failed - manual intervention needed'"}
            ]
            return ErrorRecoveryResponse(
                content=json.dumps(fallback_plan),
                recovery_plan=fallback_plan,
                recovery_approach="manual_intervention",
                estimated_recovery_steps=1,
                timestamp=asyncio.get_event_loop().time(),
                success=False,
                metadata={
                    "error": f"ErrorRecoveryPlanner exception: {str(e)}",
                    "exception_type": type(e).__name__
                }
            )
