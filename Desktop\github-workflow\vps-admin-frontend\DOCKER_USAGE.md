# Docker Usage Guide for VPS Admin System

This guide explains how to use the three Dockerfiles created for the VPS Admin system.

## Available Dockerfiles

1. **Dockerfile.vps-test** - VPS test environment with SSH access
2. **Dockerfile.backend** - FastAPI backend application
3. **Dockerfile.frontend** - React frontend application

## 1. VPS Test Environment (Dockerfile.vps-test)

Creates a Ubuntu container with SSH server and root access for testing VPS admin functionality.

### Build and Run:
```bash
# Build the VPS test image
docker build -f Dockerfile.vps-test -t vps-test-env .

# Run the VPS test container
docker run -d --name vps-test -p 2222:22 vps-test-env

# Connect via SSH (password: root)
ssh root@localhost -p 2222
```

### Credentials:
- **Root user**: `root` / `root`
- **Test user**: `testuser` / `testuser` (has sudo access)

### Features:
- SSH server running on port 22
- Sudo access configured
- Test files and directories for VPS admin testing
- Sample systemd service for testing
- Common system tools installed

## 2. Backend Application (Dockerfile.backend)

Containerizes the Python FastAPI backend with all dependencies.

### Build and Run:
```bash
# Build the backend image
docker build -f Dockerfile.backend -t vps-admin-backend .

# Run the backend container
docker run -d --name vps-backend -p 8000:8000 \
  -e GEMINI_API_KEY=your_api_key_here \
  vps-admin-backend

# Check backend health
curl http://localhost:8000/
```

### Environment Variables:
- `GEMINI_API_KEY` - Required for AI functionality
- `SERVER_HOST` - Default: 0.0.0.0
- `SERVER_PORT` - Default: 8000

### Features:
- FastAPI application with all dependencies
- Health check endpoint
- Non-root user for security
- Auto-reload enabled for development

## 3. Frontend Application (Dockerfile.frontend)

Multi-stage build for React/Vite frontend with development and production modes.

### Development Mode:
```bash
# Build development image
docker build --target development -f Dockerfile.frontend -t vps-admin-frontend-dev .

# Run development container
docker run -d --name vps-frontend-dev -p 5173:5173 \
  -v $(pwd):/app -v /app/node_modules \
  vps-admin-frontend-dev
```

### Production Mode:
```bash
# Build production image
docker build --target production -f Dockerfile.frontend -t vps-admin-frontend-prod .

# Run production container
docker run -d --name vps-frontend-prod -p 80:80 vps-admin-frontend-prod
```

### Features:
- Development mode with hot reload
- Production mode with Nginx
- API proxy configuration for backend
- Health check endpoint

## Complete System Setup

### Option 1: Manual Container Management

```bash
# 1. Start VPS test environment
docker run -d --name vps-test -p 2222:22 vps-test-env

# 2. Start backend
docker run -d --name vps-backend -p 8000:8000 \
  -e GEMINI_API_KEY=your_api_key_here \
  vps-admin-backend

# 3. Start frontend (production)
docker run -d --name vps-frontend -p 80:80 vps-admin-frontend-prod
```

### Option 2: Using Docker Network

```bash
# Create a custom network
docker network create vps-admin-network

# Start containers on the same network
docker run -d --name vps-test --network vps-admin-network -p 2222:22 vps-test-env
docker run -d --name vps-backend --network vps-admin-network -p 8000:8000 \
  -e GEMINI_API_KEY=your_api_key_here \
  vps-admin-backend
docker run -d --name vps-frontend --network vps-admin-network -p 80:80 vps-admin-frontend-prod
```

## Testing the Setup

1. **Test VPS Environment**:
   ```bash
   ssh root@localhost -p 2222
   # Password: root
   ```

2. **Test Backend**:
   ```bash
   curl http://localhost:8000/
   ```

3. **Test Frontend**:
   Open browser to `http://localhost` (or `http://localhost:5173` for dev mode)

4. **Test VPS Admin Functionality**:
   - Access the frontend
   - Configure SSH connection to `localhost:2222` with `root/root`
   - Test VPS admin commands

## Troubleshooting

### Common Issues:

1. **Port conflicts**: Change port mappings if ports are already in use
2. **Permission issues**: Ensure Docker has proper permissions
3. **API key missing**: Set GEMINI_API_KEY environment variable
4. **SSH connection fails**: Check if port 2222 is accessible

### Logs:
```bash
# View container logs
docker logs vps-test
docker logs vps-backend
docker logs vps-frontend
```

### Container Management:
```bash
# Stop all containers
docker stop vps-test vps-backend vps-frontend

# Remove all containers
docker rm vps-test vps-backend vps-frontend

# Remove all images
docker rmi vps-test-env vps-admin-backend vps-admin-frontend-prod
```

## Security Notes

- The VPS test environment is for testing only - do not use in production
- Change default passwords in production environments
- Use proper secrets management for API keys
- Consider using Docker secrets for sensitive data
- The frontend nginx configuration includes basic security headers
