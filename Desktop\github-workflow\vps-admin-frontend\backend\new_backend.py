import asyncio
import json
import uuid
from pydantic import BaseModel
from typing import Dict, Any, AsyncGenerator

from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from sse_starlette.sse import EventSourceResponse, ServerSentEvent

# --- App Initialization ---
app = FastAPI(
    title="VPS Admin Minimal Backend",
    description="A minimal text-based responder for the VPS Admin Frontend. (Patched for raw JSON streaming)",
    version="1.2.0",
)

# --- CORS Configuration ---
origins = [
    "http://localhost:5173",
    "http://127.0.0.1:5173",
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# --- In-memory "database" ---
task_states: Dict[str, Dict[str, Any]] = {}


# --- Pydantic Models ---
class StartTaskRequest(BaseModel):
    initial_prompt: str

class StartTaskResponse(BaseModel):
    task_id: str

class StreamRequest(BaseModel):
    task_id: str
    message: str


# --- Helper to create SSE events ---
def create_sse_event(event_type: str, data: Any, is_metadata: bool = False) -> ServerSentEvent:
    """Creates a Server-Sent Event for the frontend."""
    if is_metadata:
        response_data = {"type": event_type, "metadata": data}
    else:
        response_data = {"type": event_type, "content": data}

    return ServerSentEvent(
        data=json.dumps(response_data),
        event="message"
    )


# --- Stream Logic (Generator) ---
async def command_responder(task_id: str, user_message: str) -> AsyncGenerator[ServerSentEvent, None]:
    """Processes user commands and yields Server-Sent Events."""
    yield create_sse_event("info", f"Processing your message: '{user_message}'...")
    await asyncio.sleep(0.5)

    prompt = user_message.lower().strip()
    state = task_states.get(task_id, {})

    # Handle confirmation flow
    if state.get("awaiting_confirmation"):
        command_to_run = state.get("command_to_run", "ls -la")
        if "yes" in prompt:
            state["awaiting_confirmation"] = False
            yield create_sse_event("info", f"Executing '{command_to_run}'...")
            await asyncio.sleep(1)
            ssh_info = {"command": command_to_run, "stdout": "Simulated output for 'ls -la'...", "stderr": "", "exit_code": 0, "success": True, "execution_time": 0.123}
            yield create_sse_event("ssh_output", ssh_info)
            await asyncio.sleep(0.5)
            yield create_sse_event("task_end", "Command executed.")
        elif "no" in prompt:
            state["awaiting_confirmation"] = False
            yield create_sse_event("ai_response", "Okay, command cancelled.")
            yield create_sse_event("task_end", "Task aborted.")
        else:
            yield create_sse_event("ai_response", "Please respond with 'yes' or 'no'.")
        return

    # Handle different commands
    if "hi" in prompt or "hello" in prompt:
        yield create_sse_event("ai_response", "Hi! I am a minimal Python backend. Say `help` to see what I can do.")
    elif "help" in prompt:
        help_text = "Available commands:\n• **hi/hello** - Greet the system\n• **help** - Show this help\n• **run** - Run system update\n• **status** - Check disk status\n• **install nginx** - Install nginx\n• **list files** - List directory contents\n• **check memory** - Check memory usage\n• **error** - Simulate an error\n• **warn** - Show a warning"
        yield create_sse_event("summary", help_text)
    elif "run" in prompt:
        command_to_run = "sudo apt update && sudo apt upgrade -y"
        task_states[task_id] = {"awaiting_confirmation": True, "command_to_run": command_to_run}
        yield create_sse_event("command_confirmation", command_to_run)
    elif "status" in prompt:
        ssh_info = {"command": "df -h", "stdout": "Filesystem      Size  Used Avail Use% Mounted on\n/dev/vda1        79G   50G   28G  65% /\n/dev/vda2       100G   25G   70G  27% /home\ntmpfs           2.0G     0  2.0G   0% /dev/shm", "stderr": "", "exit_code": 0, "success": True, "execution_time": 0.08}
        yield create_sse_event("ssh_output", ssh_info)
    elif "install nginx" in prompt:
        command_to_run = "sudo apt update && sudo apt install nginx -y"
        task_states[task_id] = {"awaiting_confirmation": True, "command_to_run": command_to_run}
        yield create_sse_event("command_confirmation", command_to_run)
    elif "list files" in prompt:
        ssh_info = {"command": "ls -la", "stdout": "total 48\ndrwxr-xr-x  6 <USER> <GROUP> 4096 Dec 15 10:30 .\ndrwxr-xr-x  3 <USER> <GROUP> 4096 Dec 10 09:15 ..\n-rw-r--r--  1 <USER> <GROUP>  220 Dec 10 09:15 .bash_logout\n-rw-r--r--  1 <USER> <GROUP> 3771 Dec 10 09:15 .bashrc\ndrwx------  2 <USER> <GROUP> 4096 Dec 15 10:30 .cache\n-rw-r--r--  1 <USER> <GROUP>  807 Dec 10 09:15 .profile\ndrwxr-xr-x  2 <USER> <GROUP> 4096 Dec 15 10:25 Documents\ndrwxr-xr-x  2 <USER> <GROUP> 4096 Dec 15 10:25 Downloads", "stderr": "", "exit_code": 0, "success": True, "execution_time": 0.05}
        yield create_sse_event("ssh_output", ssh_info)
    elif "check memory" in prompt or "memory" in prompt:
        ssh_info = {"command": "free -h", "stdout": "               total        used        free      shared  buff/cache   available\nMem:           7.8Gi       2.1Gi       3.2Gi       256Mi       2.5Gi       5.2Gi\nSwap:          2.0Gi          0B       2.0Gi", "stderr": "", "exit_code": 0, "success": True, "execution_time": 0.03}
        yield create_sse_event("ssh_output", ssh_info)
    elif "error" in prompt:
        yield create_sse_event("error", "This is a simulated error message to test error handling in the frontend.")
    elif "warn" in prompt:
        yield create_sse_event("warning", "This is a simulated warning message. Proceed with caution!")
    else:
        yield create_sse_event("ai_response", f"Sorry, I don't understand '{prompt}'. Try typing **help** to see available commands.")


# --- API Endpoints ---
@app.get("/")
def read_root():
    return {"status": "ok"}

@app.post("/start_task", response_model=StartTaskResponse)
async def start_task(request: StartTaskRequest):
    print(f"--- /start_task endpoint HIT with prompt: '{request.initial_prompt}' ---")
    task_id = str(uuid.uuid4())
    task_states[task_id] = {"initial_prompt": request.initial_prompt}
    return {"task_id": task_id}

@app.post("/send_message")
async def stream_task(stream_request: StreamRequest):
    """
    Sends Server-Sent Events to the frontend for real-time communication.
    """
    task_id = stream_request.task_id
    message = stream_request.message
    if task_id not in task_states:
        raise HTTPException(status_code=404, detail="Task not found")
    print(f"--- /send_message HIT for task {task_id}: '{message}' ---")

    return EventSourceResponse(
        command_responder(task_id, message),
        media_type="text/event-stream"
    )

@app.get("/task/{task_id}")
async def get_task_status(task_id: str):
    """Get task status and information."""
    if task_id not in task_states:
        raise HTTPException(status_code=404, detail="Task not found")

    task_info = task_states[task_id]
    return {
        "task_id": task_id,
        "status": "active" if task_info.get("awaiting_confirmation") else "idle",
        "awaiting_confirmation": task_info.get("awaiting_confirmation", False),
        "command_to_run": task_info.get("command_to_run"),
        "initial_prompt": task_info.get("initial_prompt")
    }

@app.delete("/task/{task_id}")
async def delete_task(task_id: str):
    """Delete a task."""
    if task_id not in task_states:
        raise HTTPException(status_code=404, detail="Task not found")

    del task_states[task_id]
    return {"task_id": task_id, "message": "Task deleted successfully"}

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "active_tasks": len(task_states)}

# To run: uvicorn new_backend:app --reload --port 8000