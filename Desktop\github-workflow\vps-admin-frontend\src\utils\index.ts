/**
 * Utility functions for VPS Admin Chat application
 */

import { Task, ExecutionStats, ExportData } from '../types';
import { EXPORT_SETTINGS } from '../constants';

// Markdown processing function - returns the text for processing by a React component
export const processMarkdown = (text: string): string => {
  // For now, just return the text as-is
  // The actual markdown rendering will be handled by a React component
  return text;
};

// Time formatting utilities
export const formatUptime = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  return `${hours}h ${minutes}m ${secs}s`;
};

export const formatTimestamp = (date: Date): string => {
  return date.toLocaleTimeString();
};

export const formatDuration = (milliseconds: number): string => {
  if (milliseconds < 1000) {
    return `${milliseconds}ms`;
  }
  const seconds = Math.floor(milliseconds / 1000);
  const ms = milliseconds % 1000;
  return `${seconds}.${ms.toString().padStart(3, '0')}s`;
};

// Task management utilities
export const createTaskId = (): string => {
  return crypto.randomUUID();
};

export const createTaskTitle = (prompt: string, maxLength: number = 50): string => {
  const title = `Task: ${prompt.substring(0, maxLength)}`;
  return prompt.length > maxLength ? `${title}...` : title;
};

export const calculateTaskProgress = (stepNumber: number, totalSteps: number): number => {
  if (totalSteps === 0) return 0;
  return Math.min(100, (stepNumber / totalSteps) * 100);
};

export const calculateSuccessRate = (successful: number, total: number): number => {
  if (total === 0) return 0;
  return Math.round((successful / total) * 100);
};

// Export/Import functionality
export const exportTaskHistory = (
  taskHistory: Task[],
  executionStats: ExecutionStats,
  messages?: any[]
): void => {
  const data: ExportData = {
    taskHistory,
    executionStats,
    exportDate: new Date().toISOString(),
    ...(messages && { messages })
  };

  const blob = new Blob([JSON.stringify(data, null, EXPORT_SETTINGS.INDENT_SPACES)], {
    type: EXPORT_SETTINGS.MIME_TYPE
  });

  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `${EXPORT_SETTINGS.FILE_PREFIX}-${new Date().toISOString().split('T')[0]}${EXPORT_SETTINGS.FILE_EXTENSION}`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};

export const importTaskHistory = (file: File): Promise<ExportData> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (event) => {
      const fileContent = event.target?.result as string;
      const parseResult = safeJsonParse(fileContent);

      if (parseResult.success) {
        resolve(parseResult.data);
      } else {
        console.error('Failed to parse imported file:', parseResult.error);
        debugJsonString(fileContent);
        reject(new Error(`Invalid file format: ${parseResult.error}`));
      }
    };
    reader.onerror = () => reject(new Error('Failed to read file'));
    reader.readAsText(file);
  });
};

// Command utilities
export const isCommandLike = (text: string): boolean => {
  const commandIndicators = [
    'sudo ', 'apt ', 'yum ', 'systemctl ', 'service ', 'chmod ', 'chown ', 'cp ', 'mv ', 'rm ',
    'ls ', 'cd ', 'pwd ', 'ps ', 'kill ', 'top ', 'htop ', 'df ', 'du ', 'free ', 'uname ',
    'wget ', 'curl ', 'ssh ', 'scp ', 'rsync ', 'tar ', 'zip ', 'unzip ', 'grep ', 'find ',
    'awk ', 'sed ', 'cat ', 'head ', 'tail ', 'less ', 'more ', 'nano ', 'vim ', 'vi '
  ];

  const lowerText = text.toLowerCase().trim();
  return commandIndicators.some(cmd => lowerText.startsWith(cmd)) ||
         lowerText.startsWith('./') ||
         /[|><&]/.test(text);
};

export const filterCommandHistory = (history: string[], newCommand: string): string[] => {
  return [newCommand, ...history.filter(cmd => cmd !== newCommand)];
};

export const findCommandCompletions = (input: string, commands: string[]): string[] => {
  const lowerInput = input.toLowerCase();
  return commands.filter(cmd =>
    cmd.toLowerCase().startsWith(lowerInput) && cmd !== input
  );
};

// Validation utilities
export const validateMessage = (message: string): { isValid: boolean; error?: string } => {
  if (!message || message.trim().length === 0) {
    return { isValid: false, error: 'Message cannot be empty' };
  }
  if (message.length > 10000) {
    return { isValid: false, error: 'Message too long (max 10000 characters)' };
  }
  return { isValid: true };
};

export const validateTaskTitle = (title: string): { isValid: boolean; error?: string } => {
  if (!title || title.trim().length < 3) {
    return { isValid: false, error: 'Title must be at least 3 characters' };
  }
  if (title.length > 100) {
    return { isValid: false, error: 'Title too long (max 100 characters)' };
  }
  return { isValid: true };
};

// String utilities
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return `${text.substring(0, maxLength)}...`;
};

export const sanitizeHtml = (html: string): string => {
  const div = document.createElement('div');
  div.textContent = html;
  return div.innerHTML;
};

export const escapeRegex = (string: string): string => {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
};

// Array utilities
export const limitArray = <T>(array: T[], maxLength: number): T[] => {
  return array.slice(0, maxLength);
};

export const removeFromArray = <T>(array: T[], item: T): T[] => {
  return array.filter(i => i !== item);
};

export const moveToFront = <T>(array: T[], item: T): T[] => {
  const filtered = array.filter(i => i !== item);
  return [item, ...filtered];
};

// Object utilities
export const deepClone = <T>(obj: T): T => {
  return JSON.parse(JSON.stringify(obj));
};

export const mergeObjects = <T extends object>(target: T, source: Partial<T>): T => {
  return { ...target, ...source };
};

// Error handling utilities
export const formatError = (error: unknown): string => {
  if (error instanceof Error) {
    return error.message;
  }
  if (typeof error === 'string') {
    return error;
  }
  return 'An unknown error occurred';
};

export const isNetworkError = (error: unknown): boolean => {
  if (error instanceof Error) {
    return error.message.toLowerCase().includes('network') ||
           error.message.toLowerCase().includes('connection') ||
           error.message.toLowerCase().includes('fetch');
  }
  return false;
};

// JSON parsing utilities
export const safeJsonParse = (jsonString: string): { success: boolean; data?: any; error?: string } => {
  try {
    if (!jsonString || jsonString.trim() === '') {
      return { success: false, error: 'Empty JSON string' };
    }

    const data = JSON.parse(jsonString);
    return { success: true, data };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown parsing error';
    return { success: false, error: errorMessage };
  }
};

export const debugJsonString = (jsonString: string): void => {
  console.group('🔍 JSON Debug Information');
  console.log('Raw string:', jsonString);
  console.log('String length:', jsonString?.length || 0);
  console.log('String type:', typeof jsonString);
  console.log('First 100 chars:', jsonString?.substring(0, 100));
  console.log('Last 100 chars:', jsonString?.substring(Math.max(0, (jsonString?.length || 0) - 100)));

  // Check for common issues
  const issues = [];
  if (jsonString?.includes('\n')) issues.push('Contains newlines');
  if (jsonString?.includes('\t')) issues.push('Contains tabs');
  if (jsonString?.includes('\\')) issues.push('Contains backslashes');
  if (jsonString?.includes('"')) issues.push('Contains quotes');
  if (jsonString?.match(/[\x00-\x1F\x7F]/)) issues.push('Contains control characters');

  if (issues.length > 0) {
    console.warn('Potential issues found:', issues);
  }

  const parseResult = safeJsonParse(jsonString);
  if (parseResult.success) {
    console.log('✅ JSON is valid');
    console.log('Parsed data:', parseResult.data);
  } else {
    console.error('❌ JSON parsing failed:', parseResult.error);
  }

  console.groupEnd();
};

// Local storage utilities
export const saveToLocalStorage = (key: string, data: any): void => {
  try {
    localStorage.setItem(key, JSON.stringify(data));
  } catch (error) {
    console.warn('Failed to save to localStorage:', error);
  }
};

export const loadFromLocalStorage = <T>(key: string, defaultValue: T): T => {
  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : defaultValue;
  } catch (error) {
    console.warn('Failed to load from localStorage:', error);
    return defaultValue;
  }
};

export const removeFromLocalStorage = (key: string): void => {
  try {
    localStorage.removeItem(key);
  } catch (error) {
    console.warn('Failed to remove from localStorage:', error);
  }
};

// Debounce utility
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: number;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

// Throttle utility
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};
