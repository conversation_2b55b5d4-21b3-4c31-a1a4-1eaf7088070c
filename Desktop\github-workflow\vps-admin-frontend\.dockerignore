# Node modules and build artifacts
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dist/
build/

# Python cache and virtual environments
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
.venv/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Docker
Dockerfile*
.dockerignore

# Logs
logs/
*.log

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Coverage reports
coverage/
*.lcov

# Temporary files
tmp/
temp/

# Documentation
*.md
docs/

# Test files
test/
tests/
*.test.js
*.spec.js

# RAR archives
*.rar

# Batch files
*.bat
