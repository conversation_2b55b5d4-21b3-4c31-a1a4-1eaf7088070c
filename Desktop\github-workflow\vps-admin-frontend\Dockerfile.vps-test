# VPS Test Environment Dockerfile
# Creates a Ubuntu container with SSH server and root access for testing VPS admin functionality

FROM ubuntu:22.04

# Prevent interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive

# Update package list and install essential packages
RUN apt-get update && apt-get install -y \
    openssh-server \
    sudo \
    curl \
    wget \
    vim \
    nano \
    htop \
    net-tools \
    iputils-ping \
    systemctl \
    systemd \
    cron \
    rsyslog \
    python3 \
    python3-pip \
    nodejs \
    npm \
    git \
    unzip \
    zip \
    tar \
    gzip \
    tree \
    lsof \
    ps \
    procps \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Create SSH host keys
RUN ssh-keygen -A

# Configure SSH server
RUN mkdir -p /var/run/sshd
RUN echo 'PermitRootLogin yes' >> /etc/ssh/sshd_config
RUN echo 'PasswordAuthentication yes' >> /etc/ssh/sshd_config
RUN echo 'PubkeyAuthentication yes' >> /etc/ssh/sshd_config
RUN echo 'Port 22' >> /etc/ssh/sshd_config

# Set root password to 'root'
RUN echo 'root:root' | chpasswd

# Create a test user with sudo privileges
RUN useradd -m -s /bin/bash testuser && \
    echo 'testuser:testuser' | chpasswd && \
    usermod -aG sudo testuser

# Configure sudo to allow passwordless sudo for root and testuser
RUN echo 'root ALL=(ALL) NOPASSWD:ALL' >> /etc/sudoers
RUN echo 'testuser ALL=(ALL) NOPASSWD:ALL' >> /etc/sudoers

# Create some test directories and files for VPS admin testing
RUN mkdir -p /var/log/test-app /opt/test-app /etc/test-config
RUN echo "Test application log" > /var/log/test-app/app.log
RUN echo "#!/bin/bash\necho 'Test script executed'" > /opt/test-app/test-script.sh
RUN chmod +x /opt/test-app/test-script.sh
RUN echo "test_setting=enabled" > /etc/test-config/app.conf

# Create a simple test service
RUN echo '[Unit]\nDescription=Test Service\nAfter=network.target\n\n[Service]\nType=simple\nExecStart=/bin/bash -c "while true; do echo Test service running; sleep 30; done"\nRestart=always\n\n[Install]\nWantedBy=multi-user.target' > /etc/systemd/system/test-service.service

# Enable the test service
RUN systemctl enable test-service

# Create startup script
RUN echo '#!/bin/bash\n\
# Start SSH service\n\
service ssh start\n\
\n\
# Start systemd (if available)\n\
if command -v systemctl >/dev/null 2>&1; then\n\
    systemctl start test-service\n\
fi\n\
\n\
# Keep container running\n\
tail -f /dev/null' > /start.sh

RUN chmod +x /start.sh

# Expose SSH port
EXPOSE 22

# Set working directory
WORKDIR /root

# Start SSH service and keep container running
CMD ["/start.sh"]
