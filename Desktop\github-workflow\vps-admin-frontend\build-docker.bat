@echo off
REM Docker Build Script for VPS Admin System (Windows)
REM This script builds all Docker images for the VPS Admin system

echo 🐳 Building Docker images for VPS Admin System...
echo ==================================================

REM Check if Docker is running
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Docker is not running. Please start Docker and try again.
    pause
    exit /b 1
)

echo [SUCCESS] Docker is running.

REM Build VPS Test Environment
echo [INFO] Building VPS Test Environment...
docker build -f Dockerfile.vps-test -t vps-test-env .
if %errorlevel% neq 0 (
    echo [ERROR] Failed to build VPS Test Environment image.
    pause
    exit /b 1
)
echo [SUCCESS] VPS Test Environment image built successfully.

REM Build Backend
echo [INFO] Building Backend Application...
docker build -f Dockerfile.backend -t vps-admin-backend .
if %errorlevel% neq 0 (
    echo [ERROR] Failed to build Backend image.
    pause
    exit /b 1
)
echo [SUCCESS] Backend image built successfully.

REM Build Frontend (Development)
echo [INFO] Building Frontend Application (Development)...
docker build --target development -f Dockerfile.frontend -t vps-admin-frontend-dev .
if %errorlevel% neq 0 (
    echo [ERROR] Failed to build Frontend development image.
    pause
    exit /b 1
)
echo [SUCCESS] Frontend development image built successfully.

REM Build Frontend (Production)
echo [INFO] Building Frontend Application (Production)...
docker build --target production -f Dockerfile.frontend -t vps-admin-frontend-prod .
if %errorlevel% neq 0 (
    echo [ERROR] Failed to build Frontend production image.
    pause
    exit /b 1
)
echo [SUCCESS] Frontend production image built successfully.

echo.
echo 🎉 All Docker images built successfully!
echo ========================================
echo.
echo Available images:
echo   • vps-test-env              - VPS test environment with SSH
echo   • vps-admin-backend         - FastAPI backend application
echo   • vps-admin-frontend-dev    - React frontend (development)
echo   • vps-admin-frontend-prod   - React frontend (production)
echo.
echo Next steps:
echo   1. Review DOCKER_USAGE.md for detailed usage instructions
echo   2. Set your GEMINI_API_KEY environment variable
echo   3. Run containers using the examples in DOCKER_USAGE.md
echo.
echo Quick start:
echo   docker run -d --name vps-test -p 2222:22 vps-test-env
echo   docker run -d --name vps-backend -p 8000:8000 -e GEMINI_API_KEY=your_key vps-admin-backend
echo   docker run -d --name vps-frontend -p 80:80 vps-admin-frontend-prod
echo.
pause
